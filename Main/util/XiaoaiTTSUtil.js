import { Host } from "miot";
import { Device } from "miot/device";


export default class XiaoaiTTSUtil {

  // 支持闹钟tts的最小固件版本号
  static MIN_ALAEM_TTS_FW_VERSION = "5.3.2_0628";
  static MIN_CONTACTS_TTS_FW_VERSION = "5.3.2_0631";
  static MIN_CONTACTS_AVATAR_FW_VERSION = "5.3.2_0632";
  // 支持闹钟tts的最小米家app版本号
  static MIN_ALAEM_TTS_APP_VERSION = 10105;
  static CURRENT_VERSION = Device.lastVersion;

  static isSupportedAlarmTts() {
    // 只有米家客户端versionCode 和 固件版本号都符合，才支持上传闹钟音频
    return Host.apiLevel >= this.MIN_ALAEM_TTS_APP_VERSION && this.CURRENT_VERSION >= this.MIN_ALAEM_TTS_FW_VERSION;
  }

  static isSupportedContactsTts() {
    // 只有米家客户端versionCode 和 固件版本号都符合，才支持上传音频
    return Host.apiLevel >= this.MIN_ALAEM_TTS_APP_VERSION && this.CURRENT_VERSION >= this.MIN_CONTACTS_TTS_FW_VERSION;
  }

  static compareFwVersions(baseVersion, inputVersion) {

    // 将版本号按 "_" 和 "." 分割成数组
    const partsInput = inputVersion.replace(/[._]/g, '');
    const partsBase = baseVersion.replace(/[._]/g, '');

    return parseInt(partsInput, 10) >= parseInt(partsBase, 10);
    // // 获取较长的数组长度
    // const maxLength = Math.max(partsInput.length, partsBase.length);
    //
    // // 逐个比较每个部分
    // for (let i = 0; i < maxLength; i++) {
    //   // 将每个部分转换为整数，如果缺失则视为 0
    //   const vInput = i < partsInput.length ? parseInt(partsInput[i], 10) : 0;
    //   const vBase = i < partsBase.length ? parseInt(partsBase[i], 10) : 0;
    //
    //   if (vInput > vBase) {
    //     return true; // 输入版本号大于基础版本号
    //   } else if (vInput < vBase) {
    //     return false; // 输入版本号小于基础版本号
    //   }
    // }
    //
    // // 如果所有部分都相等，则输入版本号等于基础版本号
    // return true;
  }
}